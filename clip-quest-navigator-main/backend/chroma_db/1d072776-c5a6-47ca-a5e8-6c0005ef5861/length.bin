       to most people who have been in like any
kind of embeddings work or search work.
I think you know nodes does not really
need an introduction. Uh I think for the
past couple of years I've joked that uh
a lot of the cutting edge rag work that
people are doing now just felt like a
lot of my intern projects when I was
fine-tuning embeddings in like 2017 18
and a lot of that was hoping that we
could like average wordtovec vectors in
a sentence using genim and hope that
things worked well or you do some topic
modeling and it was in 2019 when uh you
you dropped sentence transformers and
and sentence bert and it kind really
made a lot of things click in terms of
how we can process a lot of text and at
that time I was searching over comments
and product feedback and product
descriptions. Um and then since then
you've also sort of co-authored beer and
MTB which is basically the benchmarks
that we all quote when we look at how
models perform across multiple domains
and languages. again like a very core
part of uh embedding models and you know
next week we'll have Chroma talk a
little bit more about how they do
generative evals and and create their
own personal benchmarks rather than
using MTB but generally I I think
there's been a few people that I know
that have sort of worked on semantic
search from for for this long and now
you're the VP of AI search here you just
shipped the embed for models which is I
my understanding is it's going to be
long context over images and text. And I
think one thing to call out to a lot of
people who I worked with in the past,
which is that historically there were
things like clip style embeddings, but
they were really much trained on
captions. So you could find a picture of
a giraffe or a picture of a red car, but
you know, it would never work on things
like tables and charts and diagrams. And
I think in bed 4 and these kinds of
models is the first time I've seen them
really work at at a very large scale.
And so I'm really excited to welcome
Niels to uh you know, present. And so
the stage is yours.
Oh yeah, happy to be here. Um, so in the
talk I will talk a bit like text rag,
vision rag, embed for uh challenges on
vision lms and like talk lightly on yeah
how we operate and how we work as a team
to train like these foundation models
but then also looking forward to
questions you have at the end to dive
deeper on like sections you find
interesting.
uh bit on the background. So actually
here what we do is like we train these
foundation models. So we train
foundation models for generator for
command model uh foundation models for
embeddings and rerank which is my uh
line of
expertise and then we specialize in like
these secure enterprise deployments. So,
so we target enterprise customers uh
often in like the most regulated
industries like healthcare, banking,
telecommunication, critical
infrastructure and then bring these
technology into the environment and what
me and my team are working on is like
these compass products. So the
intelligent search and discovery product
and it works extremely well. So, so it's
built on top of like the different
models has several more models included,
but you really see the difference. Um,
if you do like a comparison against
embeddings, so sadly a lot of people
like still in the field uh just do
embeddings, they think, okay, that's
that's like the yeah the golden egg.
That's what we can do use for
everything. But you really see the
difference between kind of like an
embedding solution and like a
fullyfledged enterprise search solution.
So here we have like some enterprise
data. It has invoices, tabular data and
some scanned
documents. And then if you start very
simple like which products did Jenkins
buy from us and you search over
invoices, you see was an embedding
model. It's not able to find Jenkins. So
it finds Cox Moore and then also goes
further like coms and Hinton and Atkins.
So find like all type of invoices but
not from like the the Jenkins one. and
then the the full flesh like finds the
right uh information similar. It's also
interesting to see like understanding of
dates. So if you have like a rack setup
and say hey list me all invoices from
2016. Um again just an embedding
approach gets you an invoice from 2018
and 17 and 17 and 21. So it's not really
able to like understand that you have
like a date of issue and then if I say
hey list me all the invoice from 2016
that I have to look at like date of
issue I need to understand like 2016. Um
similar if it goes into like more
complex uh nuanced information so you
say hey what's the GDP of Germany? Again
embedding models are like very very cost
grain topic driven. Um so you ask like
hey what's the GDP of Germany? And then
you see here, oh yeah, it has a lot of
like GDP included, GDP, GDP rank, GDP
growth, GDP per capita and so on. But
sadly that's about the economy of India
and so it's it's it's not a good fit. I
ask like what's the GDP of Germany and
you go all go down like you see economy
of China and economy of United States
and Russia. So it finds all the GDP
heavy ones but not like the GDP of
Germany. And for example, what we did is
like a lot of like table understanding.
So table understanding is of like a
two-dimensional thing and you say oh
yeah I have like a row with Germany of
like some header with
GDP and then you find the right
information. Um so in embed 4 we focus
on like multimodality. So still in
enterprises you have like these
lawsuits. Here's like a lawsuit from the
early 2000s against the tobacco industry
in the US. And then um if you say
something like hey did customer raise
concerns about negative health effects
of cigarettes you see when you use for
example here the the Google cip model or
the open eye clip model it finds
something for smokers but like hey uh
thank you for participating in the
smoker survey that's great here's your
bonus and it's not really like finding a
document with negative health effects
while in embed 4 we see like here we
have like the handwritten And uh and
then the customer says hey the issue is
one of life or death cancer. So raises
concern about cancer and then that
cigarettes causes
cancers. Cool. So yeah we constantly
kind of like push uh the uh the
retrieval space as it's like an
enterprise users. extremely critical
that you connect to the enterprise use
case and if you really just use
embeddings um it's like yeah maybe catch
up on like the topics and see like how
the field evolved and we kind of
constantly push so we we started with
like English paragraphs multilingual
paragraphs um support for like large
corpora 100 billion of documents
structured data tabular data low
resolution images text and image inputs
search with conditions like negations
ranges
long text documents, hierus images, scan
documents and handwriting. So we always
see like okay what's like the next
challenge uh how can we tap them in and
how can we address it and and you really
if you want to build like really good
rack solutions in our experience it
often breaks down on the retrieval part.
So the generative models they are all
very good like whatever your your flavor
is if you use open AI or Gemini or Quen
or Llama they are all very good but it
often breaks on the retrieval part
you're like you retrieve you
present to the LM the LM is
producing
output and then the user is complaining
and say hey why is the solution so bad
um cool so today I want to talk about
like from text rack to vision rack. Um
so very traditionally we have like these
text racks. So number one use case in
enterprises is PDFs and we know PDFs
have tables and we got like very
creative when we design these tables in
PDFs and then we use some parser like
mark it down or dockling or
unstructured and then we convert this to
to a markdown format and then often you
see for example with tables a format
like this where you see like columns are
shifted, rows are shifted, you do text
embeddings things you do in text LM and
then um if you ask like a question hey
what's the uh this case it's like the
the federal text rate for small
businesses in 2014
um the text LM sees something like this
picks the wrong column picks the wrong
cell produces the wrong output and again
you lose the the trust from the user you
say okay this system is saying you have
like a 3% text rate but in reality it's
maybe like an 11% text afraid. So I'm
not trusting the system. Then very often
like in many many cases when you trace
it back um you see that these pausing
step breaks that it doesn't understand
the table because people thought like
multicolon multi row headers is a good
idea. Even worse some customers uh have
images like this. Um so this is from
like a Boeing uh manual uh where they
kind of like put like a diagram. How
does a Boeing look from the
inside? And the question is like okay,
how do you convert this to markdown? Um
I saw some models trying to do like ASI
arts. Some are really just extract the
table. That's not really helpful. So
when I say hey where's the fire
extinguisher? Where do I find it? Um
it's not really helpful to say oh yeah I
find that there is fire extinguisher but
I need to like read the image and and
understand the image.
And so one one trend which are very
exciting is like going from text rack to
vision rag. So text rack is these
original PDF to markdown getting some
markdown doing some text embedding doing
some text lm and then you get like the
the question like hey where's the fire
extinguisher vision rack makes it so
much easier. you just take um the page
as an image. You get an embedding of the
image. You put it to the vision LM and
then the vision LM can reason um on
this. And in terms of code, it's quite
simple. So here's like a collab notebook
of Cohed 4 and Gemini Flash. Um so you
set up the the respective API keys and
then you say, okay, I have like
different images I want to embed. Um, so
here's like several images on financial
incomes from different companies.
um you download the image, you get like
a B 64 representation of the image, you
send the image to the endpoint, you get
the embeddings
back and then at search time, you uh
embed your question, you get the
embedding for the question, you do like
a vector search over the image
embeddings and your query embeddings and
then you get your most relevant image
and then you send this to in this case
to Gemini, with GP4 or Gwen vision and
say hey answer the question based on the
following image don't use markdown
please provide enough context for your
answer you say okay this is the image
and that's my image um that's my
question that's my image and it works
really well so so if you say hey what's
the net profit for Nike first retrieval
step finds this uh this
image and then the LMS can reason on it
and say hey according to the Nike income
statement the net profit is8 8 billion.
You can also do a bit more complex like
what are the three largest acquisitions
from Google? So it's able to read these
information from the image like whis
Motorola and median. Um so even so so
it's even like able to understand the
logos. Um, so you don't have to ensure
for example like in a PDF to markdown
you would need to ensure that logos are
translated correctly. Uh, which can
sometimes be a problem. Like if there's
just like the Apple for like the Apple
company, you're not really can sure be
sure that the the PDF to markdown
extracts the right um name for the
company. Um, similar you can also reason
on the image. So if you have something
like what would be the net profit for
Tesla without interest again you find
the right image and then you see the LM
hey based on the income statement
provided the net profit is 04 the
diagram shows interest is.3 billion
flowing into the net profit then do the
math4 billion minus.3
billion so it's 1 billion so the profit
for Tesla without interest would be 1
uh 0.1 billion. So it's like a very very
cool um system. It's like very easy to
use and especially if you have like any
of these complex visuals, it's like very
nice to reason on them. You don't have
to have the headache like how do I take
this put it to markdown
uh do all the lines do all the colors um
and or preserve it. So it extremely
simplifies your rack
system. Um to give you a bit more
history on vision embeddings, how they
work and how they evolved over time and
like yeah what's what's the history on
that. So in early 2021 um OpenAI um
published clip which became quite well
known. Um so clip is like two models.
You have a text model and you have an
image model and then taking the text
projected to a vector space. Taking the
images projected to to an image uh also
to the vector space and yeah as you said
Jason in the beginning it was trained
mostly on like image catch and this is a
cat sitting on a bench. Um the text was
like restricted to like 70 tokens was
terrible textto text search. Um low
resolution images had just like 224
pixels. So only cat images were possible
but not really like complex text
images and you have like these modality
gaps. So you see like all the text is
here and all the images is here. So if
you do like a textto image search that's
okay but in enterprise you often have
like a mixture of modalities in your
corpus. So in your corpus you have maybe
some text documents and you have like
some image documents and then if you ask
a question like hey what's the market
share of AWS
um and put it into the openi clip model
it will retrieve JP Morgan Chase uh is
the leading uh is leading the US banking
sector and you're wondering like hey why
why is it not retrieving like these nice
infographic
um and the reason is because of like
this modality gap like the all the image
embeddings and all the text embeddings
are like very very far
away. And so what we worked on in
cohhere embed v3 is um to align the
vector spaces. So first make the text
search really good up to 512 tokens. Um
ensure that the text search across
languages works extremely well. Increase
the resolution to 336 tokens. It's
better but not great. and especially um
solve the modality gap. So if you know
go in and say hey what's the market
share for AWS we find you the image with
the AWS market share. If you ask about
the market share for JP Morgan Chase it
will find you the text document um for
this. So so this was good for text and
small images up to 336 pixels. Um but
obviously we want to aim
higher. Um and this is like what we
worked on like in that 4. Um we support
high resolution images up to 1,800 *
1300 pixels. Uh what's interesting you
can interleaf text and images. So before
it was always ether text or one image.
So input a text or an image. And now we
can interle text and images. So if you
think about like a markdown, you can
have text and images
interled. Um, OCR, free text
understanding in images in 100 plus
languages. So, so you don't need to do
OCR. You can just say, okay, this is my
image, my handwriting, and I search on
this. And we also extended the context
length to 128k tokens, which will be
quite critical to really understand or
be able to understand these high
resolution images.
Um there are various use cases which are
very cool. So one is like scanned
documents. Um it used to be always
terrible to work on scanned documents
but now you just present the images of
the scanned documents embed them and
find them. Similar we know these
whiteboards. So where like you're at an
offsite you take you write it down on
the whiteboard you take a picture and
it's like always like very hard to
search on the text. But here you can
like search on the text and say okay
what's the the crown rule r rules we
defined in the
offsite also in e-commerce um nice use
cases like in e-commerce you have
product images several product images
you have a product text a title and a
description and before that it was like
always hard like you have five product
images and a product description so you
had like six embeddings and they did not
match well together but now you can just
pass it in as one and then it produces
for this embedding that stores
information across image and text. So
for example from this image we know the
genes is blue but we don't know it's a
lever 501. From the product text we know
it's a lever 501 genes but we don't know
it's a blue
jeans. But if you pass both in you get
like one embedding for like a blue lever
501 gene. So it's taking information
from the image and information from the
text together, blends it together and
produces good uh embedding output for
that. Um something I really liked is
these prompted image search. Uh so
imagine you you spend your vacation on
like a nice range in in I don't know in
Canada. You saw like a cool cowboy
wearing a nice cowboy hat. You snap the
picture and say, "Hey, the the hat
please find me this hat in green." And
then this is like for a real customer.
where it goes over the um product
catalog and then says, "Okay, in our
product catalog, this is the most
similar head we have in green." And so
it's like very nice when you can go in,
take the image, describe how you want to
change it, and then search over the text
and images you
have. Cool. Want to cover a bit on like
vision LMS, how they work, how they
operate, and then talk more on um
challenging like scaling the resolution.
So for us the most critical was like to
scale the resolution. Um as mentioned
the first uh vision LMS they had like
very low resolutions like 200 or 300
pixels. If you take like a PDF page and
yeah convert it to like these 200 pixels
it's not readable anymore. So so you
lose all the information if you run it
like low resolution.
So
um how these these vision transformer
work they take the temp uh take an image
and break it down into patches. So
patches is like small sub images totally
14 by 14 or 16 x 16
pixels and then you linearize these sub
patches and then you add some position
information. So you you say okay this is
patch one to three. So you kind of like
count from one to nine and then you put
it into like a standard transformer
model. Um the latest vision transformer
like cichlip um they can handle up to
500 time 512* 512 resolution which is
1024
tokens but again 512
um resolution is like very very hot. So,
so this is a PDF page from the Cichlip 2
paper uh resized to 512
pixels. Can still read it, but it's not
really enjoyable to read it. And if the
text gets smaller, it's like very hard
or impossible to read it. And obviously,
if you present an image to an embedding
model where the embedded model is not
able to read the text, it's not able to
uh produce good embedding. So, so if you
cannot read it, you can't produce an
embedding. And so, one big challenge is
like how do you um increase the
resolution um the resolution um scales
the the tokens quadratically. So, if you
go let's say 2,000 * 2,000 pixels, there
would be like 20k image
tokens. And there's like no pre-trained
vision transformer available for this
resolution. Um in general like
pre-training is seldom done at like 20k
context lengths. I mean from text LM we
know they claim I don't know 100k 200k 1
million 10 million context lengths but
they all none of them has been trained
at like these context lengths. So if we
look at like quen 3 they pre-trained at
like 4k
tokens and then uh using tricks to
extrapolate the longer text. So go from
like 4K text tokens to like I don't know
128k tokens. But these extrapolation is
not really possible for images. So for
text I can take the first 4,000 tokens
and then predict the token
4,01. But when I take an image and I
resize it to 512 pixels information gets
lost. So these kind of like I just use
fewer image tokens um is is not really
possible with images.
But the research community has been
quite smart um how to scale you have
vision transformers vision LMS to higher
resolutions. So one paper I really like
is the mini CPM. Um they have a vision
transformer again with like standard low
resolution 8 384 * 384 pixels
and then they take full text a full
image and resize it to like these small
resolution. So take this big image and
create like a resized version of it. But
then they also create like smaller sizes
at 384 pixels like A to F. So they have
kind of like a sliding window
um over the image and so the one image
becomes uh seven images. Um the seven
images are encoded individually with the
visual
encoder and so at the end you end up
with like seven patches times 1024
tokens each. So this one image is
encoded as like uh 7k image tokens. And
then what they have is like these
compression layer. So as an input it
gets 1024 input tokens and outputs 96
output tokens. So at the end these this
image would be encoded as 670 tokens
which is then sent to the large language
model. So one image becomes 670 tokens
and then it goes to the LM and then you
can ask the LM to hey describe what
happens
um in the image or how many tabs do you
see in the image and then the LM can
reason on these 672
tokens. Uh second approach which was
made popular by Quent to uh vision is um
kind of like sliding windows. So with
the mini CPM approach you have like this
issue that you resize the image. So so
you always force it to like 384* 384
pixels. So even if the resolution and
the scale is different you resize it. So
text can become like very blurry and
strange like if you if the the scale and
the ratio between width and height of
the image is changing you get like very
weird image looking. And then you have
like these individual
patches. And what Quen is doing is they
don't resize, but they create take the
big image and then they create like
these smaller image tokens like these
14* 14 patches and then they use window
attention. So they don't do like a full
attention over all these patches, but
they take like smaller windows of 112*
112
pixels and then compute like the the
vision transformer on these individual
small patches on these individual
windows and then they just have like
four layers that employ full attention
across all images. So this idea is bit
inspired from us humans. We also don't
see an image all at once at super high
resolution, but we have kind of like we
can scan over it. So, we take our eyes,
we scan over the image and then see um
individual things in high resolution and
then with the full attention across all
image tokens and a few layers, you're
able to to scale and get like global
image
understanding. Um training vision
embedding models is yeah surprisingly
not that difficult. Um so so that idea
is quite easy. Um so you have these
query document
pairs. So you have a query revenue of
Nike in 2025 and then you have an
image where you say this image is like a
good fit and then you put this to an
vision LM vision transformer
um it's outputting you an embedding and
then you get like the query embedding
and the document embedding and then you
force them to be close in the vector
space while negative documents should be
distant in the vector space. So the idea
is is quite easy how these systems are
trained. Um but the challenge is like
the scale. So with embed for what we
want is like zero to n text and zero to
n images on query and document side. So
query side can just be a text or it can
be a text and images or a text and
multiple images. Same on document side.
It can be just a text or just an image
or many many text and many many images.
And we want to make like suitable for
like text to text search, text to image
search, image to text search, image to
image search. Uh we want to make it
suitable for like multilingual like 100
languages, cross-lingual. So let's say
your your documents is an Arabic PDF
image and your query is Chinese and then
you're able to like search in Chinese
over the scanned documents you have in
Arabic and yeah it added a lot of like
uh complexity. So so each time you add
like a new dimension to it
uh the number of combinations scales. Um
we built yeah a lot of evaluation. So so
at some point we broke Google
spreadsheets so we're running out of
columns to put numbers in. Uh we built
evaluation for everything. So we had to
test like for all these things. Uh for
example one test is like how well can
the model find information if the text
in the image is really tiny. So we
tested like different font sizes going
from like 50 p uh 50.4 four and going
all the way down to like one point false
size to then see okay how is the
performance doing and then you do the
same like okay if I search in English
and my document is in Arabic can I find
it if uh my query is a Japanese note I
written on a napkin and have like
English documents as text can I find it
so we build like hundreds and hundreds
of training data sets uh with billions
of these query document pairs
is to really test for all these type of
things that people could imagine. Uh
font size, for colors, font types, like
if you do very strange font types, is it
able to find it? If you put it into
handwriting, is it able to find it? Uh
is it changing if you if the text is
black and the white background is white?
Or if you inverse the colors or if you
make it really colorful like I don't
know if your all your text is like
rainbow colored, is it still able to
find it? So yeah we we spent a lot of
like evaluating testing uh improving
training data and infrastructure become
became also a challenge. So training
text embedding models is comparably
easy. So if you take text a text
training
corpus it's maybe I don't know 10 50 GB
um of data and so our GPU nodes they
have like roughly a terabyte of memory
and it's very easy you take like all the
50 GB of text data you put it into
memory you have a terabyte of memory so
it's not a problem but with these images
because images are so big and especially
if you kind of like unpack them back to
like these patches and bit maps, you
have hundreds of terabytes of of data.
Uh so you quickly run out of like
memory. Um you have to think about like
your infrastructure about your
nodes. Um how to distribute it, how to
move data around, what to load, what to
load from object storage, what to load
from disk, what to put into memory. It's
a lot of like work on optimizing and
getting like a really high throughput so
that you can um iterate well and quickly
over these billions of query document
pairs you
created. Cool. To summarize vision rack
um so the pro is very nice. So uh you
have no longer complex PDF to markdown
pre-processing. You just take the PDF uh
you take an image of the PDF how it's
shown to you as a user. You take that
image get the embedding and then send
that image to the
LM. It captures all like all these
visual complex information like tables,
figures, charts um that are very hard to
convert to text. Um but there is also
still some cons. Um so I think these
these are things we will be able to
solve in the near future. um it's slower
and more expensive. So one page as a
text is about like 500 to like 2,000
tokens. But if you take a page as an
image with a resolution of 1,800 * 1200
pixels, that's 11K image tokens. So it's
it's yeah 10 10 20 times bigger, meaning
uh running the LM is like 10 20 times
slower. uh it's 10 10 10 to 20 times
more expensive to do this on the vision
domain. You also see um higher rate of
hallucination with vision LMS.
So
tibly um LMS became very good like very
low hallucination rates on text inputs.
But if you say okay here's my PDF
um yeah the 10 pages and every page has
an image you still see that vision
alarms hallucinate um produce wrong
information reads wrong numbers and yeah
invent stuff that's not in the paper.
Um but I think these are things that are
soft. So so vision retriever works
extremely well. Um vision LMS as of now
as of today have limitations. Um they
work really well if you have like these
single image inputs like hey what's the
net profit for Nike? You retrieve one
image and then ask the LM for that. But
they don't work well yet on these. I
have a question I retrieve 10 images. I
send 10 images to the LM and then hope
for a question and answer distilled
across these images. Um because LMS's
vision LMS are uh still too slow, too
expensive and hallucinates too much. But
probably give it few more iterations on
vision LMS and these are hopefully uh
problems of the past and then we can
really get rid of PDF to
markdown. Great. Thanks so much and yeah
looking forward to to questions. Thank
you. Thank you. So it sound it sounds
like for text, you know, you can buy do
something that's like top 50 text
chunks, but if it comes to images, maybe
you can only do one or two before things
get uh pretty confusing. Um, one
question that really stuck out to me was
actually a question that is is uh what
are the most important evaluation sets
or metrics that you look into these
days? And like in terms of how you
prioritize improving these metrics like
what would you say are the biggest ones?
Is it just hallucination rate? Is it
something else?
uh for retrieval or in general like text
frag. Um I I would say for retrieval for
now just because I think most people are
curious about that.
Um yeah I mean for for retrieval you
typally use NDCG10. Um so you say okay
this this is your query this is a
document these documents are relevant
and then you typally optimize for NDZG
at 10. Uh you can also optimize for
others like recall at 10 or recall at
100 but often the the correlation
between these scores like very very
high and what we do is like test very
very broadly. So, so many people fall
into the trap to very narrowly test it.
So, there's like others like Kquan,
Coke, Poly, GME for example, who are
also doing uh like vision retrieval.
They test like very very narrowly like
they just test um if your page has kind
of like black text, white background,
and is letter sized. But then in
reality, you have like people have an
image like this. one image like this uh
or it's rotated like it's rotated by 90°
and then all these models really fail on
it and so for for us what we do is like
we test longtail like really say I don't
know whiteboards we got like a bunch of
white boards and say okay like what are
like good queries on it can can we find
the right information handwriting can we
do like the handwriting on these
found like supermarket it's one of my
favorite uh It's Hungarian prospects for
supermarkets like Aldi and and so on and
and Trader Joe's to say okay like where
do I find the bananas uh in in these
prospect and who has like the best deals
on bananas and then it looks in
Hungarian on these supermarket
advertisements prospects with with all
the special deals for this
week and this gives you like a very very
robust
um model. So most models they work well
on like these MS Marco style or MTB beer
style like very short MS Marco is like
50 tokens uh on the query side there
they work well and then people kind of
like battle on it but in reality and
many enterprise use cases you have
something like this that you say okay we
have I don't know million scanned
documents from a lawsuit with
handwriting and we need to index that
and search over it and then you want
more like a general purpose model. I
guess here's a question I think many
people are actually asking a little bit
more about like what coherent compass is
you know I'm curious like for something
like coherent compass are you still
blending things like you know full text
search like how does that architecture
really work and uh I think a lot of
people are curious about what the actual
endto-end product looks like yeah um so
it's an on-prem solution for intelligent
search
um it solves multiple things So first in
enterprise
um solve the connector issue. So so in
an enterprise you have your data in all
different type of of locations for
example you have it like in a sharepoint
and in Microsoft teams and in your Gmail
maybe. So it connects to these data
sources puts the data from there
synchronize it with a vector
database and also implementing the uh
security and role permissions. So like
if I put my emails in it only me should
be able to read the emails and not my
co-workers. Similar in SharePoint you
have like these oh yeah this is like a
private shareepoint what's a shareepoint
just for the team like how do you ensure
this um
security that really data is not leaked
like that you can't go to like the the
rack system and ask like hey what is my
manager thinking about my performance
will I get a race and then it's looking
into the
inbox and then second part more on the
retrieval side is like understanding the
data so so if you connect to a share
point. What data is in there? Is it a
text? Is it a PDF? Is it an Excel? Is it
a video? Uh pausing the image, blending
text and images together. Um because in
many
cases, pure vision rag is still too
limited due to these shortcomings. So
you want to kind of like when the PDF is
text you want to put it as a text but if
the PDF has like complex visual elements
you want to put it as complex visual
element. So kind of like does the
pre-processing creates like the optimal
representation for an LM and then using
different retrieval strategies to find
information like how do you search on
tables? How do you search on invoices?
Um so so it's like a nice all-in-one
solution. You can deploy put your data
in and get the right results out. I
guess to talk a little bit more about
that like what have what has coher
learned about you know these like
chunking techniques? Is it still just
the case that you have some kind of like
800 token sliding window 50% overlap?
Are you thinking about things like
contextual retrieval? Obviously there
are like image boundaries and caption
boundaries, but I would love to learn
more about how about that.
Um so yeah in terms of chunking let's
say if it's a PDF um page level chunking
works quite well u because humans they
typically spend a lot of time to create
like a nice PDF to adhere to p page
boundaries. So like seldom you find like
a table that goes over like two pages
but it's mostly like one one page. Uh we
contextualize the information. Um, so if
you just look at like a random page in a
PDF, it's like very hard to say what's
in there. So we go in contextualize it.
Um, challenge on the contextualization
is like how to scale it. Um, so it's if
you go like these in traffic
contextualization, it's okay if you do
it maybe for a thousand PDFs, but some
customers have a bu billion PDFs and
then it becomes yeah very slow,
expensive to to contextualize it. So
highly efficient
contextualization and then but we're
also tapping into more of like these
these understanding
um gentic use cases. Um so so sometimes
you find in a PDF that it says you can
find the parameters for the model in
appendex A and so that's a chunk and
then if you say what was the parameters
for this model and then you retrieve the
chunk like oh yeah you can't find the
parameters appendex A you say oh yeah
that's that's nice to know but um it's
not really helpful for rack like for a
human it's okay because we read it and
then we jump in the PDF
So we're working on like how can um the
AI the LM like jump in the PDF. So if
it's reads like oh yeah the interesting
details are in appendex A how can it
jump to appendex A and get the
information from appendex A as well
and in those situations is it going to
be the case that we've somehow resolved
appendex A to some other text chunk or
is it very much like oh our our agent
should have the GP tool and it should
just GP for like appendix A lowerase and
then find
Um yeah, we're we're working on that. So
not not sure what's like the best tool.
Um because again PDFs can be very
diverse. So some PDF has like a digital
uh table of content in it. So you you
see like all the text uh you see all the
chapters and sections and you know the
pages and then can you can jump to the
right pages but some PDFs don't have it
or even worse some PDF has like have it
like a misleading table of contents.
Um, sometimes you can grab, but then the
challenge can be like say the section
title is not as a text but it's maybe
like an image. So they put like into
like a very nice for render it in Adobe
Photoshop and put it as an
image.
Um, but yeah, it's mostly what works is
if you have like a good search system,
you say, "Okay, um, you put a filter and
say okay, only search on this PDF." and
you search for like appendex A and then
you hope that the search system is
retrieving the right appendex A, but it
might be that it retrieves chunks where
it's also referred to appendex
A. See, I see that makes a lot of sense.
Yeah, I think in a lot of the coding
agents, scrap probably works a lot
better, but I can imagine half the text
being images is probably going to make
it very hard. Um I think a lot of
questions here are also asking about the
synthetic data and data augmentation
like how much I I can imagine a lot of
the translation for example could be uh
multilingual could be like machine
translation but um how how are you
thinking about the investments you're
making in data augmentation and
synthetic data generation?
Um yeah so a challenge here really on
the region domain was like getting the
data like for text it's easy to find
like these question answer pur for text
you go to like stack exchange you get
the questions and the answers from stack
exchange stack overflow and this gives
you like 100 million 200 million
question like question title question
body and different answers with
different
uploads and but you seldom see this for
like like no one is
really asking question as a large scale
and then say okay this image is relevant
and has the right information so no one
has like these
uh what's the market share for AWS and
then I get like this as an image as an
answer or even worse for like a PDF page
no one is asking question about cichle
and then you say oh yeah this this page
for cichlip is is relevant so we spend a
lot on like data augmentation um
synthetic data generation
um can be we kind of like went to to
Latte. So we we created a bunch of
different lattes styles. We put standard
benchmark let's say from beer into latte
rendered it in latte with different font
sizes and colors and layouts. So let's
say one column, two column, three
columns and then you get like a very
nice comparison. So let's say you you
take the natural questions data set from
from beer and then you render it in all
these different styles and you know okay
if I do text to text search that's my
performance. If I do text to image
search and the image is I don't know
gray text on white background with font
size four that's the performance and
then you see kind of like what's the
delta um between these um same with
multi lingual so you machine translate
it to other languages for testing
um a lot of crawling finding interesting
data sets so finding PDFs in Arabic in
Korean and Japanese
uh looked for like where do you have
these information looked for like
parallel information so for example
stock companies they they often put out
annual reports in English then the
identical report in Arabic for example
and then collected these from let's say
Saudi Aramco where we say okay this this
is a report in English that's the same
report every page aligns in Arabic and
then you use it for evaluation and
testing
Wikipedia also kind of like go to
Wikipedia and then print Wikipedia. So,
so you call a Chrome, you say print it
to PDF and then you convert the PDF to
images and then you have like the raw
Wikipedia and you have the printed
Wikipedia of the same article and then
you can ask questions on it or um in a
browser you render the full Wikipedia as
like one long image and then you can ask
like a question like I don't know you
zoom in and say okay for section five
you ask like I don't know what was
Barack Obama's favorite sports in
primary school and then on the other
side you have like the full rendered
Wikipedia page and then you use this for
training and evaluation. So
it's a lot of synthetic data generation
um a lot of creativity. So so many
people fall into the trap to have like
these one pipeline. So, so if you look
also at like CoQ