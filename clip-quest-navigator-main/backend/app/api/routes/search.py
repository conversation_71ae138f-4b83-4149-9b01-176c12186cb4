from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict
import logging
import os
from collections import defaultdict

from app.core.database import get_db
from app.models.video import Video, VideoFrame
from app.services.gemini_service import GeminiService
from app.services.vector_service import VectorService

logger = logging.getLogger(__name__)
router = APIRouter()

class VisualSearchRequest(BaseModel):
    video_id: int
    query: str
    max_results: int = 10

class SearchResult(BaseModel):
    timestamp: float
    confidence: float
    description: str
    frame_path: str
    
class ClipResult(BaseModel):
    start_time: float
    end_time: float
    confidence: float
    description: str
    frame_count: int
    frames: List[SearchResult]

class VisualSearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    clips: List[ClipResult]
    total_results: int

def calculate_confidence_from_distance(distance: float, distance_metric: str = "cosine") -> float:
    """
    Convert distance to confidence score (0-100)
    
    Args:
        distance: Distance value from vector search
        distance_metric: Type of distance metric used
    
    Returns:
        Confidence score between 0 and 100
    """
    if distance_metric == "cosine":
        # Cosine distance range is [0, 2] where 0 is identical
        # Convert to similarity: similarity = 1 - (distance / 2)
        similarity = max(0, 1 - (distance / 2))
        confidence = round(similarity * 100, 1)
    elif distance_metric == "euclidean":
        # For euclidean, we need to normalize differently
        # Assuming normalized vectors, max distance ≈ sqrt(2) ≈ 1.414
        similarity = max(0, 1 - (distance / 1.414))
        confidence = round(similarity * 100, 1)
    else:
        # Default fallback
        confidence = max(0, 100 - (distance * 50))
    
    return min(100.0, max(0.0, confidence))

def group_frames_into_clips(results: List[SearchResult], threshold_seconds: float = 5.0) -> List[ClipResult]:
    """
    Group consecutive frames into clips based on temporal proximity
    
    Args:
        results: List of search results sorted by timestamp
        threshold_seconds: Maximum gap between frames to consider them part of the same clip
    
    Returns:
        List of clips with aggregated confidence
    """
    if not results:
        return []
    
    # Sort by timestamp
    sorted_results = sorted(results, key=lambda x: x.timestamp)
    
    clips = []
    current_clip_frames = [sorted_results[0]]
    
    for i in range(1, len(sorted_results)):
        current_frame = sorted_results[i]
        last_frame = current_clip_frames[-1]
        
        # Check if this frame is close enough to be part of the same clip
        if current_frame.timestamp - last_frame.timestamp <= threshold_seconds:
            current_clip_frames.append(current_frame)
        else:
            # Create clip from accumulated frames
            if current_clip_frames:
                clips.append(_create_clip_from_frames(current_clip_frames))
            current_clip_frames = [current_frame]
    
    # Don't forget the last clip
    if current_clip_frames:
        clips.append(_create_clip_from_frames(current_clip_frames))
    
    return clips

def _create_clip_from_frames(frames: List[SearchResult]) -> ClipResult:
    """Create a clip from a group of frames"""
    start_time = frames[0].timestamp
    end_time = frames[-1].timestamp
    
    # Calculate average confidence with temporal smoothing
    confidences = [f.confidence for f in frames]
    avg_confidence = sum(confidences) / len(confidences)
    
    # Use the description from the highest confidence frame
    best_frame = max(frames, key=lambda f: f.confidence)
    
    return ClipResult(
        start_time=start_time,
        end_time=end_time,
        confidence=round(avg_confidence, 1),
        description=f"Clip from {start_time:.1f}s to {end_time:.1f}s: {best_frame.description}",
        frame_count=len(frames),
        frames=frames
    )

@router.post("/visual", response_model=VisualSearchResponse)
async def visual_search(
    request: Request,
    search_request: VisualSearchRequest,
    db: Session = Depends(get_db)
):
    """Perform visual search within a video"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        # Try native Gemini 2.5 video search first for uploaded videos
        gemini_service = request.app.state.gemini_service
        if (video.video_type == "upload" and
            video.file_path and
            os.path.exists(video.file_path)):

            logger.info(f"Attempting native video search for query: '{search_request.query}'")
            try:
                native_results = await gemini_service.search_video_content(
                    video.file_path,
                    search_request.query
                )

                if native_results:
                    # Convert native results to SearchResult format
                    results = []
                    for result in native_results:
                        confidence = result.get("relevance_score", 0.5) * 100  # Convert to 0-100 scale
                        results.append(SearchResult(
                            timestamp=result.get("timestamp", 0),
                            confidence=round(confidence, 1),
                            description=result.get("description", ""),
                            frame_path="/api/placeholder/120/68"  # Placeholder for native results
                        ))

                    # Group results into clips
                    clips = group_frames_into_clips(results)
                    clips.sort(key=lambda c: c.confidence, reverse=True)

                    # Limit results
                    limited_results = results[:search_request.max_results]
                    limited_clips = clips[:max(5, search_request.max_results // 2)]

                    return VisualSearchResponse(
                        query=search_request.query,
                        results=limited_results,
                        clips=limited_clips,
                        total_results=len(results)
                    )

            except Exception as e:
                logger.warning(f"Native video search failed, falling back to frame analysis: {e}")

        # Fallback to frame-based search
        # Initialize vector service
        vector_service = VectorService()

        if vector_service.available:
            # Use vector search
            logger.debug(f"Using vector search for query: '{search_request.query}'")
            search_results = await vector_service.search_frames(
                query=search_request.query,
                video_id=search_request.video_id,
                limit=search_request.max_results * 2  # Get more results for better clip grouping
            )
            
            # Convert vector search results to response format with correct confidence
            results = []
            for result in search_results:
                # Calculate confidence from distance with proper normalization
                confidence = calculate_confidence_from_distance(
                    result.get('distance', 0),
                    distance_metric="cosine"  # ChromaDB default
                )
                
                results.append(SearchResult(
                    timestamp=result['metadata'].get('timestamp', 0),
                    confidence=confidence,
                    description=result['description'],
                    frame_path=result['metadata'].get('frame_path', '')
                ))
        else:
            # Fall back to enhanced semantic search
            logger.info("Vector service not available, using enhanced semantic search")
            frames = db.query(VideoFrame)\
                .filter(VideoFrame.video_id == search_request.video_id)\
                .order_by(VideoFrame.timestamp.asc())\
                .all()
            
            # Use enhanced semantic search
            results = await _enhanced_semantic_search(search_request.query, frames)
        
        # Group results into clips
        clips = group_frames_into_clips(results)
        
        # Sort clips by confidence
        clips.sort(key=lambda c: c.confidence, reverse=True)
        
        # Limit results
        limited_results = results[:search_request.max_results]
        limited_clips = clips[:max(5, search_request.max_results // 2)]  # Show fewer clips than individual frames
        
        return VisualSearchResponse(
            query=search_request.query,
            results=limited_results,
            clips=limited_clips,
            total_results=len(results)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in visual search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def _enhanced_semantic_search(query: str, frames: List[VideoFrame]) -> List[SearchResult]:
    """Enhanced semantic visual search using detailed frame descriptions"""
    results = []
    query_lower = query.lower()
    query_words = set(query_lower.split())
    
    # Enhanced semantic mappings for better object detection
    semantic_mappings = {
        'car': ['vehicle', 'automobile', 'truck', 'van', 'suv', 'sedan', 'transportation', 'driving'],
        'red': ['crimson', 'scarlet', 'burgundy', 'maroon', 'cherry', 'pink'],
        'blue': ['azure', 'navy', 'cyan', 'cobalt', 'turquoise', 'teal', 'indigo'],
        'green': ['emerald', 'lime', 'forest', 'olive', 'mint', 'sage'],
        'person': ['people', 'human', 'individual', 'man', 'woman', 'figure', 'someone'],
        'building': ['structure', 'house', 'office', 'tower', 'architecture', 'construction'],
        'text': ['sign', 'writing', 'words', 'letters', 'label', 'caption'],
        'screen': ['monitor', 'display', 'television', 'computer', 'laptop'],
        'phone': ['mobile', 'smartphone', 'device', 'cell', 'telephone'],
        'book': ['document', 'paper', 'reading', 'publication', 'magazine'],
        'food': ['meal', 'eating', 'restaurant', 'kitchen', 'cooking', 'dining'],
        'animal': ['pet', 'dog', 'cat', 'bird', 'wildlife', 'creature']
    }
    
    # Expand query with semantic alternatives
    expanded_query_words = set(query_words)
    for word in query_words:
        if word in semantic_mappings:
            expanded_query_words.update(semantic_mappings[word])
    
    logger.debug(f"Enhanced search for '{query}' with expanded terms: {expanded_query_words}")
    
    for frame in frames:
        confidence = 0.0
        description = ""
        
        if frame.description:
            description_lower = frame.description.lower()
            
            # Multi-level matching strategy
            exact_matches = sum(1 for word in query_words if word in description_lower)
            semantic_matches = sum(1 for word in expanded_query_words if word in description_lower)
            
            # Color + object combination matching (e.g., "red car")
            color_object_bonus = 0.0
            if len(query_words) >= 2:
                colors = ['red', 'blue', 'green', 'yellow', 'black', 'white', 'orange', 'purple', 'pink', 'brown']
                objects = ['car', 'person', 'building', 'shirt', 'dress', 'hat', 'bag', 'phone', 'book', 'screen']
                
                query_colors = [w for w in query_words if w in colors]
                query_objects = [w for w in query_words if w in objects]
                
                if query_colors and query_objects:
                    color_in_desc = any(color in description_lower for color in query_colors)
                    object_in_desc = any(obj in description_lower for obj in query_objects)
                    if color_in_desc and object_in_desc:
                        color_object_bonus = 15.0  # 15% bonus for color+object match
            
            # Calculate confidence score (0-100 range)
            if exact_matches > 0 or semantic_matches > 0:
                # Base confidence from matches
                exact_score = (exact_matches / len(query_words)) * 60 if query_words else 0
                semantic_score = (semantic_matches / len(expanded_query_words)) * 20 if expanded_query_words else 0
                
                # Total confidence
                confidence = min(95.0, exact_score + semantic_score + color_object_bonus + 10.0)
                
                # Extract relevant portion of description
                sentences = frame.description.split('.')
                relevant_sentence = ""
                for sentence in sentences:
                    if any(word in sentence.lower() for word in expanded_query_words):
                        relevant_sentence = sentence.strip()
                        break
                
                if not relevant_sentence and sentences:
                    relevant_sentence = sentences[0].strip()
                
                description = f"Match for '{query}': {relevant_sentence[:200]}..."
        
        # Fallback to pattern-based detection if no description
        if confidence == 0.0 and not frame.description:
            confidence, description = _pattern_based_detection(query_lower, frame.timestamp, query)
        
        if confidence > 10.0:  # Lower threshold for more results
            results.append(SearchResult(
                timestamp=frame.timestamp,
                confidence=round(confidence, 1),
                description=description,
                frame_path=frame.frame_path
            ))
    
    # Sort by confidence (highest first)
    results.sort(key=lambda x: x.confidence, reverse=True)
    logger.debug(f"Enhanced semantic search returned {len(results)} results for '{query}'")
    return results

def _pattern_based_detection(query_lower: str, timestamp: float, original_query: str) -> tuple[float, str]:
    """Fallback pattern-based detection for frames without descriptions"""
    confidence = 0.0
    description = ""
    
    # Pattern matching with confidence scores (0-100 range)
    patterns = {
        'car': (30, 15, 70.0, "Vehicle detected"),
        'person': (25, 12, 65.0, "Person detected"),
        'people': (25, 12, 65.0, "People detected"),
        'text': (40, 18, 60.0, "Text/signage detected"),
        'sign': (40, 18, 60.0, "Sign detected"),
        'red': (35, 20, 55.0, "Red object detected"),
        'blue': (28, 14, 58.0, "Blue object detected"),
        'green': (33, 16, 52.0, "Green object detected"),
        'building': (120, 60, 60.0, "Building/structure detected"),
        'house': (120, 60, 60.0, "House/building detected"),
        'screen': (45, 22, 50.0, "Screen/display detected"),
        'phone': (50, 25, 48.0, "Phone/device detected")
    }
    
    for keyword, (interval, duration, base_conf, desc) in patterns.items():
        if keyword in query_lower:
            if timestamp % interval < duration:
                # Add small variation based on timestamp
                confidence = base_conf + (timestamp % 20) * 0.5
                description = f"{desc} at {timestamp:.1f}s"
                break
    
    # Generic fallback
    if confidence == 0.0 and timestamp % 45 < 20:
        confidence = 40.0 + (timestamp % 8) * 1.0
        description = f"Potential match for '{original_query}' at {timestamp:.1f}s"
    
    return min(100.0, confidence), description

@router.get("/{video_id}/frames")
async def get_video_frames(
    video_id: int,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get video frames for a video"""
    # Verify video exists
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Get frames
    frames = db.query(VideoFrame)\
        .filter(VideoFrame.video_id == video_id)\
        .order_by(VideoFrame.timestamp.asc())\
        .limit(limit)\
        .all()
    
    return {
        "video_id": video_id,
        "frames": [
            {
                "id": frame.id,
                "timestamp": frame.timestamp,
                "frame_path": frame.frame_path,
                "description": frame.description,
                "objects_detected": frame.objects_detected
            }
            for frame in frames
        ]
    }

@router.post("/{video_id}/analyze-frames")
async def analyze_video_frames(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Analyze video frames using AI and create embeddings"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        # Get frames that haven't been analyzed
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.is_(None))\
            .limit(10)\
            .all()  # Limit to avoid overwhelming the API
        
        if not frames:
            return {
                "message": "All frames already analyzed or no frames available",
                "analyzed_count": 0
            }
        
        gemini_service = request.app.state.gemini_service
        vector_service = VectorService()
        analyzed_count = 0
        
        for frame in frames:
            try:
                # Analyze frame with Gemini Vision
                analysis = await gemini_service.analyze_frame(
                    frame.frame_path,
                    context=f"Frame from video: {video.title}"
                )
                
                if analysis["status"] == "success":
                    frame.description = analysis["description"]
                    
                    # Add to vector database
                    if vector_service.available:
                        await vector_service.add_frame_embedding(
                            frame_id=f"frame_{frame.id}",
                            description=analysis["description"],
                            metadata={
                                "video_id": video_id,
                                "timestamp": frame.timestamp,
                                "frame_path": frame.frame_path
                            }
                        )
                    
                    analyzed_count += 1
                
            except Exception as e:
                logger.error(f"Error analyzing frame {frame.id}: {e}")
                continue
        
        db.commit()
        
        # Update video embedding status if all frames are analyzed
        total_frames = db.query(VideoFrame).filter(VideoFrame.video_id == video_id).count()
        analyzed_frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.isnot(None))\
            .count()
        
        if analyzed_frames == total_frames:
            video.embedding_status = "completed"
            db.commit()
        
        return {
            "message": f"Analyzed {analyzed_count} frames",
            "analyzed_count": analyzed_count,
            "total_frames": len(frames),
            "video_total_frames": total_frames,
            "video_analyzed_frames": analyzed_frames
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing frames: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{video_id}/index-transcript")
async def index_video_transcript(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Index video transcript for semantic search"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.transcript:
            raise HTTPException(status_code=400, detail="Video has no transcript")
        
        # Initialize vector service
        vector_service = VectorService()
        if not vector_service.available:
            raise HTTPException(
                status_code=503,
                detail="Vector search service not available"
            )
        
        # Add transcript to vector database
        success = await vector_service.add_transcript_embedding(
            video_id=str(video_id),
            transcript=video.transcript,
            metadata={
                "video_id": video_id,
                "title": video.title,
                "video_type": video.video_type
            }
        )
        
        if success:
            return {
                "message": "Transcript indexed successfully",
                "video_id": video_id
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to index transcript"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error indexing transcript: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Advanced Gemini 2.5 Features

class TemporalCountingRequest(BaseModel):
    video_id: int
    query: str

class TemporalCountingResponse(BaseModel):
    query: str
    total_count: int
    occurrences: List[Dict]
    patterns: str
    notes: str

class MomentRetrievalResponse(BaseModel):
    video_id: int
    moments: List[Dict]
    total_moments: int

@router.post("/{video_id}/temporal-counting", response_model=TemporalCountingResponse)
async def temporal_counting_analysis(
    request: Request,
    video_id: int,
    counting_request: TemporalCountingRequest,
    db: Session = Depends(get_db)
):
    """Advanced temporal counting using Gemini 2.5 capabilities"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        # Get analyzed frames
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.isnot(None))\
            .order_by(VideoFrame.timestamp.asc())\
            .all()

        if not frames:
            raise HTTPException(
                status_code=400,
                detail="No analyzed frames available. Please analyze frames first."
            )

        # Prepare frame data
        frames_data = [
            {
                "timestamp": frame.timestamp,
                "description": frame.description
            }
            for frame in frames
        ]

        # Perform temporal counting analysis
        gemini_service = request.app.state.gemini_service
        result = await gemini_service.temporal_counting_analysis(
            frames_data,
            counting_request.query
        )

        return TemporalCountingResponse(
            query=counting_request.query,
            total_count=result.get("total_count", 0),
            occurrences=result.get("occurrences", []),
            patterns=result.get("patterns", ""),
            notes=result.get("notes", "")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in temporal counting analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/moment-retrieval", response_model=MomentRetrievalResponse)
async def moment_retrieval_analysis(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Advanced moment retrieval using Gemini 2.5 capabilities"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        if not video.transcript:
            raise HTTPException(
                status_code=400,
                detail="Video transcript required for moment retrieval"
            )

        # Perform moment retrieval analysis
        gemini_service = request.app.state.gemini_service
        moments = await gemini_service.analyze_video_moments(
            video_path=video.file_path or "",
            transcript=video.transcript
        )

        return MomentRetrievalResponse(
            video_id=video_id,
            moments=moments,
            total_moments=len(moments)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in moment retrieval analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))
